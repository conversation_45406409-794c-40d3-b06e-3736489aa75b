/**
 * WhisperProcessPool.ts
 * 
 * True process pooling implementation for whisper-cli to achieve concurrent processing
 * and reduced queue wait times. While individual processes still need to load models,
 * this approach provides significant performance improvements through parallelization.
 * 
 * Architecture:
 * - ProcessWorker: Manages individual whisper-cli process lifecycle
 * - ProcessPool: Coordinates multiple workers for concurrent processing
 * - Intelligent request routing and worker health monitoring
 */

import { EventEmitter } from 'events'
import { spawn, ChildProcess } from 'child_process'
import { v4 as uuidv4 } from 'uuid'

export type WorkerState = 'idle' | 'processing' | 'completed' | 'failed' | 'terminated'

export interface ProcessWorkerOptions {
  workerId: string
  whisperCliPath: string
  modelPath: string
  whisperCppDir: string
  language: string
  requestTimeout: number
}

export interface WorkerRequest {
  id: string
  audioFilePath: string
  resolve: (result: any) => void
  reject: (error: Error) => void
  timestamp: number
}

export interface WorkerStats {
  requestsProcessed: number
  totalProcessingTime: number
  averageLatency: number
  lastActivity: number
  failureCount: number
}

/**
 * ProcessWorker manages a single whisper-cli process lifecycle
 */
export class ProcessWorker extends EventEmitter {
  private options: ProcessWorkerOptions
  private state: WorkerState = 'idle'
  private currentRequest: WorkerRequest | null = null
  private currentProcess: ChildProcess | null = null
  private stats: WorkerStats
  private lastError: string | undefined

  constructor(options: ProcessWorkerOptions) {
    super()
    this.options = options
    this.stats = {
      requestsProcessed: 0,
      totalProcessingTime: 0,
      averageLatency: 0,
      lastActivity: Date.now(),
      failureCount: 0
    }

    console.log(`[ProcessWorker:${this.options.workerId}] Initialized`)
  }

  /**
   * Check if worker is available for new requests
   */
  isAvailable(): boolean {
    return this.state === 'idle' && this.currentRequest === null
  }

  /**
   * Assign a request to this worker
   */
  async processRequest(request: WorkerRequest): Promise<void> {
    if (!this.isAvailable()) {
      throw new Error(`Worker ${this.options.workerId} is not available`)
    }

    this.currentRequest = request
    this.state = 'processing'
    this.stats.lastActivity = Date.now()

    console.log(`[ProcessWorker:${this.options.workerId}] Processing request ${request.id}`)

    try {
      const result = await this.executeWhisperCli(request.audioFilePath)
      const processingTime = Date.now() - request.timestamp

      this.updateStats(processingTime, true)

      if (result) {
        result.processingTime = processingTime
        console.log(`[ProcessWorker:${this.options.workerId}] Request ${request.id} completed in ${processingTime}ms`)
        request.resolve(result)
      } else {
        console.warn(`[ProcessWorker:${this.options.workerId}] Request ${request.id} failed - no result`)
        request.resolve(null)
      }

      this.state = 'completed'
    } catch (error) {
      console.error(`[ProcessWorker:${this.options.workerId}] Request ${request.id} failed:`, error)
      this.updateStats(Date.now() - request.timestamp, false)
      this.lastError = error.message
      this.state = 'failed'
      request.reject(error as Error)
    } finally {
      this.currentRequest = null
      this.state = 'idle'
      this.emit('requestCompleted', this.options.workerId)
    }
  }

  /**
   * Execute whisper-cli process for the request
   */
  private async executeWhisperCli(audioFilePath: string): Promise<any> {
    const args = [
      '-l', this.options.language,
      '-m', this.options.modelPath,
      '-f', audioFilePath,
      '--no-timestamps',
      '--threads', '1', // Single thread for minimal overhead
      '--processors', '1',
      '--beam-size', '1', // Greedy decoding for speed
      '--best-of', '1',
      '--temperature', '0.0',
      '--no-fallback'
    ]

    console.log(`[ProcessWorker:${this.options.workerId}] Executing: ${this.options.whisperCliPath} ${args.join(' ')}`)

    return new Promise((resolve) => {
      const startTime = Date.now()
      let isResolved = false
      let timeoutHandle: NodeJS.Timeout | null = null

      this.currentProcess = spawn(this.options.whisperCliPath, args, {
        stdio: ['pipe', 'pipe', 'pipe'],
        cwd: this.options.whisperCppDir
      })

      let stdout = ''
      let stderr = ''

      // Helper function to resolve once and cleanup
      const resolveOnce = (result: any) => {
        if (isResolved) return
        isResolved = true

        if (timeoutHandle) {
          clearTimeout(timeoutHandle)
          timeoutHandle = null
        }

        this.currentProcess = null
        resolve(result)
      }

      this.currentProcess.stdout.on('data', (data) => {
        stdout += data.toString()
      })

      this.currentProcess.stderr.on('data', (data) => {
        stderr += data.toString()
      })

      this.currentProcess.on('close', (code) => {
        const processingTime = Date.now() - startTime

        if (code === 0) {
          const text = this.parseWhisperOutput(stdout)
          if (text && text.trim()) {
            resolveOnce({
              success: true,
              text: text.trim(),
              segments: [],
              processingTime
            })
          } else {
            this.lastError = 'No text extracted from audio'
            resolveOnce(null)
          }
        } else {
          console.error(`[ProcessWorker:${this.options.workerId}] whisper-cli failed with exit code ${code}`)
          console.error(`[ProcessWorker:${this.options.workerId}] stderr: ${stderr}`)
          this.lastError = `CLI process failed with exit code ${code}`
          resolveOnce(null)
        }
      })

      this.currentProcess.on('error', (error) => {
        console.error(`[ProcessWorker:${this.options.workerId}] whisper-cli spawn error:`, error)
        this.lastError = `CLI spawn error: ${error.message}`
        resolveOnce(null)
      })

      // Timeout handling
      timeoutHandle = setTimeout(() => {
        if (!isResolved && this.currentProcess) {
          this.currentProcess.kill('SIGTERM')
          const timeoutTime = Date.now() - startTime
          console.warn(`[ProcessWorker:${this.options.workerId}] whisper-cli timeout after ${timeoutTime}ms`)
          this.lastError = `CLI process timeout after ${timeoutTime}ms`
          resolveOnce(null)
        }
      }, this.options.requestTimeout)
    })
  }

  /**
   * Parse whisper-cli output to extract clean text
   */
  private parseWhisperOutput(output: string): string {
    try {
      if (!output || !output.trim()) {
        return ''
      }

      const lines = output.split('\n')
      const transcriptionLines = lines.filter(line => {
        const trimmed = line.trim()

        if (!trimmed) return false

        // Skip system info lines
        if (trimmed.includes('whisper_model_load:') ||
            trimmed.includes('system_info:') ||
            trimmed.includes('whisper_print_timings:') ||
            trimmed.includes('load time =') ||
            trimmed.includes('mel time =') ||
            trimmed.includes('sample time =') ||
            trimmed.includes('encode time =') ||
            trimmed.includes('decode time =') ||
            trimmed.includes('total time =') ||
            trimmed.includes('operator():') ||
            trimmed.includes('processing') ||
            trimmed.includes('threads') ||
            trimmed.includes('processors')) {
          return false
        }

        // Skip timestamp lines
        if (trimmed.match(/^\[\d{2}:\d{2}:\d{2}\.\d{3} --> \d{2}:\d{2}:\d{2}\.\d{3}\]/)) {
          return false
        }

        return true
      })

      return transcriptionLines.join(' ').trim()
    } catch (error) {
      console.error(`[ProcessWorker:${this.options.workerId}] Error parsing whisper output:`, error)
      return ''
    }
  }

  /**
   * Update worker statistics
   */
  private updateStats(processingTime: number, success: boolean): void {
    if (success) {
      this.stats.requestsProcessed++
      this.stats.totalProcessingTime += processingTime
      this.stats.averageLatency = this.stats.totalProcessingTime / this.stats.requestsProcessed
      this.lastError = undefined
    } else {
      this.stats.failureCount++
    }
    this.stats.lastActivity = Date.now()
  }

  /**
   * Get worker status and statistics
   */
  getStatus() {
    return {
      workerId: this.options.workerId,
      state: this.state,
      isAvailable: this.isAvailable(),
      currentRequest: this.currentRequest?.id || null,
      stats: { ...this.stats },
      lastError: this.lastError
    }
  }

  /**
   * Terminate the worker and cleanup
   */
  terminate(): void {
    this.state = 'terminated'

    if (this.currentProcess) {
      this.currentProcess.kill('SIGTERM')
      this.currentProcess = null
    }

    if (this.currentRequest) {
      this.currentRequest.reject(new Error('Worker terminated'))
      this.currentRequest = null
    }

    console.log(`[ProcessWorker:${this.options.workerId}] Terminated`)
  }
}

export interface ProcessPoolOptions {
  poolSize?: number
  whisperCliPath: string
  modelPath: string
  whisperCppDir: string
  language: string
  requestTimeout: number
}

export interface PoolStats {
  totalWorkers: number
  availableWorkers: number
  busyWorkers: number
  totalRequestsProcessed: number
  averageLatency: number
  poolUtilization: number
}

/**
 * ProcessPool manages multiple ProcessWorkers for concurrent whisper-cli processing
 */
export class ProcessPool extends EventEmitter {
  private options: ProcessPoolOptions
  private workers: Map<string, ProcessWorker> = new Map()
  private requestQueue: WorkerRequest[] = []
  private isInitialized = false

  constructor(options: ProcessPoolOptions) {
    super()
    this.options = {
      poolSize: 3, // Default pool size
      ...options
    }

    console.log(`[ProcessPool] Initialized with pool size: ${this.options.poolSize}`)
  }

  /**
   * Initialize the process pool with workers
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('[ProcessPool] Already initialized')
      return
    }

    console.log(`[ProcessPool] Creating ${this.options.poolSize} workers...`)

    for (let i = 0; i < this.options.poolSize!; i++) {
      const workerId = `worker-${i + 1}`
      const worker = new ProcessWorker({
        workerId,
        whisperCliPath: this.options.whisperCliPath,
        modelPath: this.options.modelPath,
        whisperCppDir: this.options.whisperCppDir,
        language: this.options.language,
        requestTimeout: this.options.requestTimeout
      })

      // Listen for worker completion to process queue
      worker.on('requestCompleted', () => {
        this.processQueue()
      })

      this.workers.set(workerId, worker)
    }

    this.isInitialized = true
    console.log(`[ProcessPool] Initialized with ${this.workers.size} workers`)
    this.emit('initialized')
  }

  /**
   * Submit a request to the process pool
   */
  async processRequest(audioFilePath: string): Promise<any> {
    if (!this.isInitialized) {
      throw new Error('ProcessPool not initialized')
    }

    return new Promise((resolve, reject) => {
      const request: WorkerRequest = {
        id: uuidv4(),
        audioFilePath,
        resolve,
        reject,
        timestamp: Date.now()
      }

      this.requestQueue.push(request)
      console.log(`[ProcessPool] Queued request ${request.id}, queue length: ${this.requestQueue.length}`)

      // Try to process immediately
      this.processQueue()
    })
  }

  /**
   * Process the request queue by assigning requests to available workers
   */
  private processQueue(): void {
    if (this.requestQueue.length === 0) {
      return
    }

    // Find available workers
    const availableWorkers = Array.from(this.workers.values()).filter(worker => worker.isAvailable())

    if (availableWorkers.length === 0) {
      console.log(`[ProcessPool] No available workers, ${this.requestQueue.length} requests queued`)
      return
    }

    // Assign requests to available workers
    const requestsToProcess = Math.min(this.requestQueue.length, availableWorkers.length)

    for (let i = 0; i < requestsToProcess; i++) {
      const request = this.requestQueue.shift()!
      const worker = availableWorkers[i]

      console.log(`[ProcessPool] Assigning request ${request.id} to ${worker.getStatus().workerId}`)

      // Process request asynchronously
      worker.processRequest(request).catch(error => {
        console.error(`[ProcessPool] Worker processing failed:`, error)
      })
    }

    console.log(`[ProcessPool] Assigned ${requestsToProcess} requests, ${this.requestQueue.length} remaining in queue`)
  }

  /**
   * Get pool statistics and status
   */
  getStatus(): PoolStats {
    const workers = Array.from(this.workers.values())
    const availableWorkers = workers.filter(w => w.isAvailable()).length
    const busyWorkers = workers.length - availableWorkers

    const totalRequests = workers.reduce((sum, w) => sum + w.getStatus().stats.requestsProcessed, 0)
    const totalTime = workers.reduce((sum, w) => sum + w.getStatus().stats.totalProcessingTime, 0)
    const averageLatency = totalRequests > 0 ? totalTime / totalRequests : 0

    return {
      totalWorkers: workers.length,
      availableWorkers,
      busyWorkers,
      totalRequestsProcessed: totalRequests,
      averageLatency,
      poolUtilization: workers.length > 0 ? (busyWorkers / workers.length) * 100 : 0
    }
  }

  /**
   * Get detailed status of all workers
   */
  getWorkerStatuses() {
    return Array.from(this.workers.values()).map(worker => worker.getStatus())
  }

  /**
   * Get current queue status
   */
  getQueueStatus() {
    return {
      queueLength: this.requestQueue.length,
      queuedRequests: this.requestQueue.map(r => ({ id: r.id, timestamp: r.timestamp }))
    }
  }

  /**
   * Shutdown the process pool and terminate all workers
   */
  async shutdown(): Promise<void> {
    console.log('[ProcessPool] Shutting down...')

    // Reject all queued requests
    while (this.requestQueue.length > 0) {
      const request = this.requestQueue.shift()!
      request.reject(new Error('ProcessPool shutting down'))
    }

    // Terminate all workers
    for (const worker of this.workers.values()) {
      worker.terminate()
    }

    this.workers.clear()
    this.isInitialized = false

    console.log('[ProcessPool] Shutdown completed')
    this.emit('shutdown')
  }
}
