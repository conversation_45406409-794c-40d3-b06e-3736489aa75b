/**
 * PersistentWhisperEngine.ts
 *
 * IMPORTANT: This is currently a QUEUED WHISPER-CLI SPAWNER, not a truly persistent engine.
 * Each transcription request spawns a new whisper-cli process that loads the model from scratch.
 *
 * Current behavior:
 * - Each request incurs ~878ms model loading overhead
 * - No persistent processes or model caching
 * - Provides queue management and optimized CLI parameters
 * - Fallback mechanism for streaming transcription
 *
 * TODO: Replace with faster-whisper or native addon for true persistence
 *
 * Key features:
 * - Request queuing and management
 * - Optimized whisper-cli parameters for speed
 * - Realistic timeout handling for model loading + transcription
 * - Proper error handling and monitoring
 * - Thread-safe operation
 */

import { EventEmitter } from 'events'
import { spawn, ChildProcess } from 'child_process'
import { promises as fs } from 'fs'
import * as path from 'path'
import * as os from 'os'
import { v4 as uuidv4 } from 'uuid'

export interface PersistentWhisperOptions {
  modelSize?: 'tiny.en' | 'base.en' | 'small.en'
  language?: string
  maxConcurrentRequests?: number
  processTimeout?: number // Timeout for individual CLI process spawns (not persistent process monitoring)
  restartThreshold?: number
  requestTimeout?: number // Timeout for individual transcription requests
}

export interface TranscriptionRequest {
  id: string
  audioFilePath: string
  resolve: (result: TranscriptionResult | null) => void
  reject: (error: Error) => void
  timestamp: number
}

export interface TranscriptionResult {
  success: boolean
  text: string
  segments?: any[]
  duration?: number
  processingTime?: number
}

export interface EngineStatus {
  isRunning: boolean
  isReady: boolean
  processId?: number
  modelLoaded: boolean
  requestsProcessed: number
  averageLatency: number
  lastError?: string
}

/**
 * Queued Whisper-CLI Spawner (currently NOT truly persistent)
 *
 * This class manages a queue of whisper-cli transcription requests.
 * Each request spawns a new whisper-cli process that loads the model from scratch.
 */
export class PersistentWhisperEngine extends EventEmitter {
  private options: Required<PersistentWhisperOptions & { requestTimeout: number }>
  private whisperProcess: ChildProcess | null = null // Currently unused - no persistent process
  private isInitialized = false
  private isReady = false
  private requestQueue: TranscriptionRequest[] = []
  private activeRequest: TranscriptionRequest | null = null
  private modelCache: any = null

  // Performance tracking
  private requestsProcessed = 0
  private totalProcessingTime = 0
  private failureCount = 0
  private lastError: string | undefined = undefined

  // Process management (currently tracks queue activity, not persistent processes)
  private processStartTime = 0
  private lastActivity = 0 // Renamed from lastHeartbeat for accuracy
  private activityMonitorInterval: NodeJS.Timeout | null = null // Renamed from heartbeatInterval
  
  constructor(options: PersistentWhisperOptions = {}) {
    super()

    this.options = {
      modelSize: options.modelSize || 'tiny.en',
      language: options.language || 'en',
      maxConcurrentRequests: options.maxConcurrentRequests || 10,
      processTimeout: options.processTimeout || 30000, // 30 seconds for queue monitoring
      restartThreshold: options.restartThreshold || 5, // Restart after 5 failures
      requestTimeout: options.requestTimeout || 2000 // 2 seconds for individual requests (878ms model load + transcription)
    }

    console.log('[PersistentWhisperEngine] Initialized as queued CLI spawner with options:', this.options)
  }

  /**
   * Initialize the persistent whisper engine
   */
  async initialize(): Promise<boolean> {
    if (this.isInitialized) {
      console.log('[PersistentWhisperEngine] Already initialized')
      return true
    }

    try {
      console.log('[PersistentWhisperEngine] Starting initialization...')
      
      // Initialize model cache
      await this.initializeModelCache()
      
      if (!this.modelCache) {
        throw new Error('Failed to initialize model cache')
      }
      
      // Start the persistent whisper process
      await this.startWhisperProcess()
      
      // Start activity monitoring
      this.startActivityMonitoring()
      
      this.isInitialized = true
      console.log('[PersistentWhisperEngine] Initialization completed successfully')
      this.emit('initialized')
      
      return true
    } catch (error) {
      console.error('[PersistentWhisperEngine] Initialization failed:', error)
      this.emit('error', error)
      return false
    }
  }

  /**
   * Initialize model cache with paths and validation
   */
  private async initializeModelCache(): Promise<void> {
    try {
      const projectRoot = path.resolve(process.cwd(), '../..')
      const whisperCppDir = path.join(projectRoot, 'node_modules', 'nodejs-whisper', 'cpp', 'whisper.cpp')
      const whisperCliPath = path.join(whisperCppDir, 'build', 'bin', 'whisper-cli')
      const modelPath = path.join(whisperCppDir, 'models', `ggml-${this.options.modelSize}.bin`)

      // Validate paths
      if (!await this.fileExists(whisperCliPath)) {
        throw new Error(`whisper-cli not found at: ${whisperCliPath}`)
      }
      if (!await this.fileExists(modelPath)) {
        throw new Error(`Model not found at: ${modelPath}`)
      }

      this.modelCache = {
        isLoaded: false,
        modelPath,
        whisperCliPath,
        whisperCppDir,
        lastUsed: Date.now()
      }

      console.log('[PersistentWhisperEngine] Model cache initialized:', {
        modelPath: this.modelCache.modelPath,
        whisperCliPath: this.modelCache.whisperCliPath
      })
    } catch (error) {
      console.error('[PersistentWhisperEngine] Failed to initialize model cache:', error)
      throw error
    }
  }

  /**
   * Initialize the queue manager (no persistent process is actually started)
   */
  private async startWhisperProcess(): Promise<void> {
    // NOTE: No actual persistent process is started since whisper-cli doesn't support it
    // This method just initializes the queue management system

    try {
      console.log('[PersistentWhisperEngine] Initializing queue manager (no persistent process)...')

      // Mark as ready for queue processing
      this.isReady = true
      this.processStartTime = Date.now()
      this.lastActivity = Date.now()

      console.log('[PersistentWhisperEngine] Queue manager ready for transcription requests')
      console.log('[PersistentWhisperEngine] NOTE: Each request will spawn individual whisper-cli processes')
      this.emit('ready')

    } catch (error) {
      console.error('[PersistentWhisperEngine] Failed to initialize queue manager:', error)
      throw error
    }
  }

  /**
   * Transcribe audio file using the persistent engine
   */
  async transcribe(audioFilePath: string): Promise<TranscriptionResult | null> {
    if (!this.isInitialized || !this.isReady) {
      throw new Error('PersistentWhisperEngine not initialized or ready')
    }

    return new Promise((resolve, reject) => {
      const request: TranscriptionRequest = {
        id: uuidv4(),
        audioFilePath,
        resolve,
        reject,
        timestamp: Date.now()
      }

      // Add to queue
      this.requestQueue.push(request)
      console.log(`[PersistentWhisperEngine] Queued request ${request.id}, queue length: ${this.requestQueue.length}`)
      
      // Process queue
      this.processQueue()
    })
  }

  /**
   * Process the transcription request queue
   */
  private async processQueue(): Promise<void> {
    if (this.activeRequest || this.requestQueue.length === 0) {
      return
    }

    const request = this.requestQueue.shift()!
    this.activeRequest = request

    try {
      console.log(`[PersistentWhisperEngine] Processing request ${request.id}`)
      const startTime = Date.now()
      
      // Use optimized whisper-cli execution (similar to current approach but optimized)
      const result = await this.executeWhisperCli(request.audioFilePath)
      
      const processingTime = Date.now() - startTime
      this.updatePerformanceMetrics(processingTime)
      
      if (result) {
        result.processingTime = processingTime
        console.log(`[PersistentWhisperEngine] Request ${request.id} completed in ${processingTime}ms`)
        request.resolve(result)
      } else {
        console.log(`[PersistentWhisperEngine] Request ${request.id} failed - no result`)
        request.resolve(null)
      }
      
    } catch (error) {
      console.error(`[PersistentWhisperEngine] Request ${request.id} failed:`, error)
      this.failureCount++
      this.lastError = `Request failed: ${error.message}`
      this.lastActivity = Date.now() // Update activity even on failure
      request.reject(error as Error)
    } finally {
      this.activeRequest = null
      // Process next request in queue
      if (this.requestQueue.length > 0) {
        setImmediate(() => this.processQueue())
      }
    }
  }

  /**
   * Execute whisper-cli with optimized parameters for persistent engine
   */
  private async executeWhisperCli(audioFilePath: string): Promise<TranscriptionResult | null> {
    if (!this.modelCache) {
      throw new Error('Model cache not initialized')
    }

    try {
      // Ultra-optimized parameters for persistent engine
      const args = [
        '-l', this.options.language,
        '-m', this.modelCache.modelPath,
        '-f', audioFilePath,
        '--no-timestamps',
        '--threads', '1', // Single thread for minimal overhead
        '--processors', '1',
        '--beam-size', '1', // Greedy decoding for speed
        '--best-of', '1',
        '--temperature', '0.0',
        '--no-fallback'
      ]

      console.log(`[PersistentWhisperEngine] Executing: ${this.modelCache.whisperCliPath} ${args.join(' ')}`)

      return new Promise((resolve) => {
        const startTime = Date.now()
        let isResolved = false
        let timeoutHandle: NodeJS.Timeout | null = null

        const whisperProcess = spawn(this.modelCache.whisperCliPath, args, {
          stdio: ['pipe', 'pipe', 'pipe'],
          cwd: this.modelCache.whisperCppDir
        })

        let stdout = ''
        let stderr = ''

        // Helper function to resolve once and cleanup
        const resolveOnce = (result: any) => {
          if (isResolved) return
          isResolved = true

          // Clear timeout to prevent stray timeout logs
          if (timeoutHandle) {
            clearTimeout(timeoutHandle)
            timeoutHandle = null
          }

          resolve(result)
        }

        whisperProcess.stdout.on('data', (data) => {
          stdout += data.toString()
        })

        whisperProcess.stderr.on('data', (data) => {
          stderr += data.toString()
        })

        whisperProcess.on('close', (code) => {
          const processingTime = Date.now() - startTime

          if (code === 0) {
            const text = this.parseWhisperOutput(stdout)
            if (text && text.trim()) {
              console.log(`[PersistentWhisperEngine] CLI process completed successfully in ${processingTime}ms`)
              resolveOnce({
                success: true,
                text: text.trim(),
                segments: [],
                processingTime
              })
            } else {
              console.warn(`[PersistentWhisperEngine] CLI process completed but no text extracted`)
              this.lastError = 'No text extracted from audio'
              resolveOnce(null)
            }
          } else {
            console.error(`[PersistentWhisperEngine] whisper-cli failed with exit code ${code}`)
            console.error(`[PersistentWhisperEngine] stderr: ${stderr}`)
            this.lastError = `CLI process failed with exit code ${code}`
            resolveOnce(null)
          }
        })

        whisperProcess.on('error', (error) => {
          console.error(`[PersistentWhisperEngine] whisper-cli spawn error:`, error)
          this.lastError = `CLI spawn error: ${error.message}`
          resolveOnce(null)
        })

        // Realistic timeout accounting for model loading (878ms) + transcription time
        timeoutHandle = setTimeout(() => {
          if (!isResolved) {
            whisperProcess.kill('SIGTERM')
            const timeoutTime = Date.now() - startTime
            console.warn(`[PersistentWhisperEngine] whisper-cli timeout after ${timeoutTime}ms (includes ~878ms model loading)`)
            this.lastError = `CLI process timeout after ${timeoutTime}ms`
            resolveOnce(null)
          }
        }, this.options.requestTimeout) // Configurable timeout (default 2000ms)
      })
    } catch (error) {
      console.error('[PersistentWhisperEngine] Execute whisper-cli error:', error)
      return null
    }
  }

  /**
   * Parse whisper-cli output to extract clean text
   */
  private parseWhisperOutput(output: string): string {
    try {
      if (!output || !output.trim()) {
        return ''
      }

      const lines = output.split('\n')
      const transcriptionLines = lines.filter(line => {
        const trimmed = line.trim()

        if (!trimmed) return false

        // Skip system info lines
        if (trimmed.includes('whisper_model_load:') ||
            trimmed.includes('system_info:') ||
            trimmed.includes('whisper_print_timings:') ||
            trimmed.includes('load time =') ||
            trimmed.includes('mel time =') ||
            trimmed.includes('sample time =') ||
            trimmed.includes('encode time =') ||
            trimmed.includes('decode time =') ||
            trimmed.includes('total time =') ||
            trimmed.includes('operator():') ||
            trimmed.includes('processing') ||
            trimmed.includes('threads') ||
            trimmed.includes('processors')) {
          return false
        }

        // Skip timestamp lines
        if (trimmed.match(/^\[\d{2}:\d{2}:\d{2}\.\d{3} --> \d{2}:\d{2}:\d{2}\.\d{3}\]/)) {
          return false
        }

        return true
      })

      return transcriptionLines.join(' ').trim()
    } catch (error) {
      console.error('[PersistentWhisperEngine] Error parsing whisper output:', error)
      return ''
    }
  }

  /**
   * Update performance metrics and activity tracking
   */
  private updatePerformanceMetrics(processingTime: number): void {
    this.requestsProcessed++
    this.totalProcessingTime += processingTime
    this.lastActivity = Date.now()
    this.lastError = undefined // Clear error on successful completion
  }

  /**
   * Get current engine status
   */
  getStatus(): EngineStatus {
    return {
      isRunning: false, // No persistent process running
      isReady: this.isReady,
      processId: undefined, // No persistent process
      modelLoaded: false, // Model is loaded fresh for each request
      requestsProcessed: this.requestsProcessed,
      averageLatency: this.requestsProcessed > 0 ? this.totalProcessingTime / this.requestsProcessed : 0,
      lastError: this.lastError
    }
  }

  /**
   * Start activity monitoring (monitors queue activity, not persistent processes)
   */
  private startActivityMonitoring(): void {
    this.activityMonitorInterval = setInterval(() => {
      const now = Date.now()

      // Check for excessive inactivity (no requests processed recently)
      if (this.lastActivity > 0 && (now - this.lastActivity) > this.options.processTimeout) {
        console.info('[PersistentWhisperEngine] No recent activity - queue may be idle')
        // Note: This is informational only, not an error condition
      }

      // Check failure threshold and reset if needed
      if (this.failureCount >= this.options.restartThreshold) {
        console.warn(`[PersistentWhisperEngine] Failure threshold reached (${this.failureCount} failures), resetting counters`)
        this.failureCount = 0 // Reset failure count instead of "restarting" non-existent process
        this.emit('warning', 'High failure rate detected, counters reset')
      }
    }, 10000) // Check every 10 seconds (less frequent than before)
  }

  /**
   * Reset the queue manager (no actual process restart needed)
   */
  async restart(): Promise<boolean> {
    console.log('[PersistentWhisperEngine] Resetting queue manager...')

    try {
      await this.shutdown()
      this.failureCount = 0
      this.requestsProcessed = 0
      this.totalProcessingTime = 0
      this.lastError = undefined

      return await this.initialize()
    } catch (error) {
      console.error('[PersistentWhisperEngine] Reset failed:', error)
      this.lastError = `Reset failed: ${error.message}`
      return false
    }
  }

  /**
   * Shutdown the queue manager
   */
  async shutdown(): Promise<void> {
    console.log('[PersistentWhisperEngine] Shutting down queue manager...')

    this.isReady = false
    this.isInitialized = false

    // Clear activity monitoring
    if (this.activityMonitorInterval) {
      clearInterval(this.activityMonitorInterval)
      this.activityMonitorInterval = null
    }

    // Note: No persistent process to terminate

    // Reject pending requests
    while (this.requestQueue.length > 0) {
      const request = this.requestQueue.shift()!
      request.reject(new Error('Queue manager shutting down'))
    }

    if (this.activeRequest) {
      this.activeRequest.reject(new Error('Queue manager shutting down'))
      this.activeRequest = null
    }

    this.emit('shutdown')
    console.log('[PersistentWhisperEngine] Queue manager shutdown completed')
  }

  /**
   * Check if file exists
   */
  private async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath)
      return true
    } catch {
      return false
    }
  }

  /**
   * Get queue status
   */
  getQueueStatus(): { queueLength: number; activeRequest: string | null } {
    return {
      queueLength: this.requestQueue.length,
      activeRequest: this.activeRequest?.id || null
    }
  }

  /**
   * Clear the request queue
   */
  clearQueue(): void {
    while (this.requestQueue.length > 0) {
      const request = this.requestQueue.shift()!
      request.reject(new Error('Queue cleared'))
    }
    console.log('[PersistentWhisperEngine] Request queue cleared')
  }
}
